-- Test script to validate duplicate payment fix
-- Run this script after applying the migration to verify the fix works

-- ============================================================================
-- Setup test data
-- ============================================================================

-- Create a test subscription (if not exists)
DO $$
DECLARE
    test_user_id UUID := '00000000-0000-0000-0000-000000000001';
    test_subscription_id UUID;
BEGIN
    -- Insert test subscription
    INSERT INTO subscriptions (
        id,
        user_id,
        short_id,
        name,
        actual_price,
        regular_price,
        subscription_type_id,
        payment_type_id,
        company_id,
        payment_date,
        is_trial
    ) VALUES (
        '11111111-1111-1111-1111-111111111111',
        test_user_id,
        'TEST-001',
        'Test Subscription for Duplicate Payment Fix',
        10.00,
        10.00,
        1, -- Assuming subscription type 1 exists
        1, -- Assuming payment type 1 exists  
        1, -- Assuming company 1 exists
        CURRENT_DATE,
        false
    ) ON CONFLICT (id) DO NOTHING;
    
    RAISE NOTICE 'Test subscription created or already exists';
END $$;

-- ============================================================================
-- Test 1: Subscription Type Change (should create history, not payment)
-- ============================================================================

-- Clear existing history for clean test
DELETE FROM subscription_history 
WHERE subscription_id = '11111111-1111-1111-1111-111111111111';

-- Update subscription type (this should trigger handle_subscription_type_change)
UPDATE subscriptions 
SET subscription_type_id = 2 
WHERE id = '11111111-1111-1111-1111-111111111111';

-- Check results
SELECT 
    'Subscription Type Change Test' as test_name,
    COUNT(*) as total_records,
    COUNT(*) FILTER (WHERE status = 'history') as history_records,
    COUNT(*) FILTER (WHERE status = 'paid') as payment_records,
    CASE 
        WHEN COUNT(*) FILTER (WHERE status = 'paid') = 0 THEN '✅ PASS'
        ELSE '❌ FAIL - Created payment records'
    END as result
FROM subscription_history 
WHERE subscription_id = '11111111-1111-1111-1111-111111111111'
  AND type = 'subscription_type_change';

-- ============================================================================
-- Test 2: Promo Change (should create history, not payment)
-- ============================================================================

-- Clear existing history for clean test
DELETE FROM subscription_history 
WHERE subscription_id = '11111111-1111-1111-1111-111111111111';

-- Update promo settings (this should trigger handle_promo_change)
UPDATE subscriptions 
SET is_promo_active = true, promo_price = 8.00, promo_cycles = 3
WHERE id = '11111111-1111-1111-1111-111111111111';

-- Check results
SELECT 
    'Promo Change Test' as test_name,
    COUNT(*) as total_records,
    COUNT(*) FILTER (WHERE status = 'history') as history_records,
    COUNT(*) FILTER (WHERE status = 'paid') as payment_records,
    CASE 
        WHEN COUNT(*) FILTER (WHERE status = 'paid') = 0 THEN '✅ PASS'
        ELSE '❌ FAIL - Created payment records'
    END as result
FROM subscription_history 
WHERE subscription_id = '11111111-1111-1111-1111-111111111111'
  AND type = 'promo_change';

-- ============================================================================
-- Test 3: Discount Change (should create history, not payment)
-- ============================================================================

-- Clear existing history for clean test
DELETE FROM subscription_history 
WHERE subscription_id = '11111111-1111-1111-1111-111111111111';

-- Update discount settings (this should trigger handle_discount_change)
UPDATE subscriptions 
SET is_discount_active = true, discount_amount = 2.00, discount_type = 'fixed'
WHERE id = '11111111-1111-1111-1111-111111111111';

-- Check results
SELECT 
    'Discount Change Test' as test_name,
    COUNT(*) as total_records,
    COUNT(*) FILTER (WHERE status = 'history') as history_records,
    COUNT(*) FILTER (WHERE status = 'paid') as payment_records,
    CASE 
        WHEN COUNT(*) FILTER (WHERE status = 'paid') = 0 THEN '✅ PASS'
        ELSE '❌ FAIL - Created payment records'
    END as result
FROM subscription_history 
WHERE subscription_id = '11111111-1111-1111-1111-111111111111'
  AND type = 'discount_change';

-- ============================================================================
-- Test 4: Actual Payment Processing (should still work)
-- ============================================================================

-- Clear existing history for clean test
DELETE FROM subscription_history 
WHERE subscription_id = '11111111-1111-1111-1111-111111111111';

-- Store original dates for comparison
DO $$
DECLARE
    original_last_paid_date DATE;
    original_next_payment_date DATE;
    new_last_paid_date DATE;
    new_next_payment_date DATE;
BEGIN
    -- Get original dates
    SELECT last_paid_date, next_payment_date 
    INTO original_last_paid_date, original_next_payment_date
    FROM subscriptions 
    WHERE id = '11111111-1111-1111-1111-111111111111';
    
    -- Insert an actual payment record
    INSERT INTO subscription_history (
        subscription_id,
        payment_date,
        amount,
        status,
        type,
        payment_type_id
    ) VALUES (
        '11111111-1111-1111-1111-111111111111',
        CURRENT_DATE,
        10.00,
        'paid',
        'payment',
        1
    );
    
    -- Check if subscription dates were updated
    SELECT last_paid_date, next_payment_date 
    INTO new_last_paid_date, new_next_payment_date
    FROM subscriptions 
    WHERE id = '11111111-1111-1111-1111-111111111111';
    
    -- Report results
    RAISE NOTICE 'Payment Processing Test:';
    RAISE NOTICE 'Original last_paid_date: %, New: %', original_last_paid_date, new_last_paid_date;
    RAISE NOTICE 'Original next_payment_date: %, New: %', original_next_payment_date, new_next_payment_date;
    
    IF new_last_paid_date = CURRENT_DATE THEN
        RAISE NOTICE '✅ PASS - Payment processing still works';
    ELSE
        RAISE NOTICE '❌ FAIL - Payment processing broken';
    END IF;
END $$;

-- ============================================================================
-- Test 5: Duplicate Payment Prevention
-- ============================================================================

-- Try to insert a duplicate payment (should fail)
DO $$
BEGIN
    BEGIN
        INSERT INTO subscription_history (
            subscription_id,
            payment_date,
            amount,
            status,
            type,
            payment_type_id
        ) VALUES (
            '11111111-1111-1111-1111-111111111111',
            CURRENT_DATE,
            10.00,
            'paid',
            'payment',
            1
        );
        
        RAISE NOTICE '❌ FAIL - Duplicate payment was allowed';
    EXCEPTION WHEN OTHERS THEN
        RAISE NOTICE '✅ PASS - Duplicate payment prevented: %', SQLERRM;
    END;
END $$;

-- ============================================================================
-- Test 6: History Records Should Not Trigger Payment Processing
-- ============================================================================

-- Clear existing history for clean test
DELETE FROM subscription_history 
WHERE subscription_id = '11111111-1111-1111-1111-111111111111';

-- Store original dates
DO $$
DECLARE
    original_last_paid_date DATE;
    original_next_payment_date DATE;
    new_last_paid_date DATE;
    new_next_payment_date DATE;
BEGIN
    -- Get original dates
    SELECT last_paid_date, next_payment_date 
    INTO original_last_paid_date, original_next_payment_date
    FROM subscriptions 
    WHERE id = '11111111-1111-1111-1111-111111111111';
    
    -- Insert a history record (should NOT trigger payment processing)
    INSERT INTO subscription_history (
        subscription_id,
        payment_date,
        amount,
        status,
        type,
        notes
    ) VALUES (
        '11111111-1111-1111-1111-111111111111',
        CURRENT_DATE,
        10.00,
        'history',
        'test_change',
        'Test history record'
    );
    
    -- Check if subscription dates were updated (they shouldn't be)
    SELECT last_paid_date, next_payment_date 
    INTO new_last_paid_date, new_next_payment_date
    FROM subscriptions 
    WHERE id = '11111111-1111-1111-1111-111111111111';
    
    -- Report results
    IF original_last_paid_date = new_last_paid_date AND original_next_payment_date = new_next_payment_date THEN
        RAISE NOTICE '✅ PASS - History records do not trigger payment processing';
    ELSE
        RAISE NOTICE '❌ FAIL - History record triggered payment processing';
    END IF;
END $$;

-- ============================================================================
-- Summary Report
-- ============================================================================

SELECT 
    '=== DUPLICATE PAYMENT FIX TEST SUMMARY ===' as summary;

SELECT 
    type,
    status,
    COUNT(*) as count,
    'Records created during testing' as description
FROM subscription_history 
WHERE subscription_id = '11111111-1111-1111-1111-111111111111'
GROUP BY type, status
ORDER BY type, status;

-- Cleanup test data
DELETE FROM subscription_history 
WHERE subscription_id = '11111111-1111-1111-1111-111111111111';

DELETE FROM subscriptions 
WHERE id = '11111111-1111-1111-1111-111111111111';

SELECT '✅ Test cleanup completed' as cleanup_status;
