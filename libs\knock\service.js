import { Knock } from "@knocklabs/node";
import { logError, logInfo } from "@/libs/sentry";
import { isChannelAvailable } from "@/utils/plan-utils";

let knockInstance = null;

export function getKnockServer() {
  try {
    const apiKey = process.env.KNOCK_SECRET_API_KEY;
    // console.log('🔑 DEBUG: Knock API Key status:', {
    //   hasKey: !!apiKey,
    //   keyLength: apiKey?.length,
    //   keyPrefix: apiKey?.substring(0, 7) + '...'
    // });

    if (!apiKey) {
      throw new Error('KNOCK_SECRET_API_KEY is missing or empty');
    }

    knockInstance = new Knock({ apiKey });
  } catch (error) {
    logError('Failed to initialize Knock', error);
    console.error('❌ Knock initialization error:', error.message);
  }

  return knockInstance;
}

/**
 * Notification templates available in Knock
 */
export const NOTIFICATION_TEMPLATES = {
  // Cat: Subscriptions (User's tracked subscriptions)
  SUBSCRIPTION_DUE: "user-subscription-due",
  TRIAL_ENDING: "trial-expiring",
  // Cat: Sharing
  SHARING_INVITE: "subscription-share-invite",
  ACCESS_ACCEPTED: "subscription-share-accepted",
  ACCESS_REVOKED: "subscription-share-revoked",
  ACCESS_LEVEL_CHANGED: "subscription-share-level-changed",
  // Cat: SubsKeepr Billing (Your revenue)
  SK_SUBSCRIPTION_CREATED: "subscription-created",
  SK_PAYMENT_RECOVERED: "payment-recovered",
  SK_PAYMENT_FAILED: "subskeepr-payment-failed",
  SK_PAYMENT_RECEIVED: "payment-received",
  SK_SUBSCRIPTION_CANCELED: "subscription-cancelled",
  SK_SUBSCRIPTION_UPDATED: "subskeepr-subscription-updated",
  // Additional billing events
  CHECKOUT_EXPIRED: "checkout-abandoned",
  PAYMENT_METHOD_UPDATED: "payment-method-updated",
  PAYMENT_ACTION_REQUIRED: "payment-action-required",
};


/**
 * Handles communication with the Knock notification service.
 */
export class KnockService {
  constructor() {
    this.knock = getKnockServer();
  }

  /**
   * Identifies a user in Knock, creating or updating their information.
   * Uses the PUT /users/:user_id endpoint with rate limit handling.
   */
  async identifyUser(userId, userData) {
    const maxRetries = 3;
    const baseDelay = 1000; // 1 second

    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        const response = await fetch(`${process.env.KNOCK_API_URL}/users/${userId}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${process.env.KNOCK_SECRET_API_KEY}`,
          },
          body: JSON.stringify({
            name: userData.display_name,
            email: userData.email,
            avatar: userData.display_avatar_url,
            phone_number: userData.phone,
            locale: userData.locale ?? 'en-US',
            timezone: userData.timezone,
            ...(userData.preferences && { preferences: userData.preferences }),
            ...(userData.channel_data && { channel_data: userData.channel_data }),
            // Custom properties
            stripe_customer_id: userData.stripe_customer_id,
            pricing_tier: userData.pricing_tier?.toLowerCase() ?? 'basic',
            has_notifications: userData.has_notifications ?? false,
            is_admin: userData.is_admin ?? false,
          }),
        });

        if (response.status === 429) {
          // Rate limited - wait and retry
          const delay = baseDelay * Math.pow(2, attempt); // Exponential backoff
          console.log(`Rate limited, retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(
            `Failed to identify user: ${response.status} ${errorData.message || response.statusText}`
          );
        }

        return await response.json();
      } catch (error) {
        if (attempt === maxRetries - 1) {
          logError("Error identifying user in Knock for user: " + userId, error);
          throw error;
        }
      }
    }
  }

  /**
   * Sets channel preferences for a user based on their pricing tier and UI preferences.
   * @param {string} userId
   * @param {string} pricingTier
   * @param {object} preferences - { email: true/false, in_app_feed: true/false, push: true/false }
   */
  async setChannelPreferences(userId, pricingTier, preferences = {}) {
    try {
      const allChannels = ['email', 'in_app_feed', 'push'];
      const channelSettings = {};
      const categoryChannelSettings = {};

      // Dynamically load categories from env (comma-separated, fallback to default)
      const categoriesEnv = process.env.NOTIFICATION_CATEGORIES || 'SubsKeepr,Subscriptions,Trials';
      const allCategories = categoriesEnv.split(',').map(c => c.trim()).filter(Boolean);

      // Check if this is an empty preferences object (initial setup)
      const hasExistingPreferences = Object.keys(preferences).length > 0;

      // Per-category channel preferences
      for (const category of allCategories) {
        for (const channel of allChannels) {
          if (isChannelAvailable(channel, pricingTier)) {
            // Required logic for billing and security categories (email/in_app_feed always ON)
            const requiredCategories = ['SubsKeeprBilling', 'SubsKeeprSecurity'];
            const isRequiredCategory = requiredCategories.includes(category);
            const isRequired = isRequiredCategory && (channel === 'email' || channel === 'in_app_feed');

            // preferences structure: { [category]: { [channel]: boolean } }
            const userPref = preferences[category]?.[channel];

            let subscribed;
            if (isRequired) {
              subscribed = true; // Always true for required channels
            } else if (!hasExistingPreferences) {
              // Default to true for initial setup (except push notifications)
              subscribed = channel !== 'push';
            } else {
              subscribed = !!userPref; // Use user's existing preference
            }

            // Include the preference in the request
            categoryChannelSettings[`categories.${category}.${channel}`] = { subscribed };
          }
        }
      }

      const requestBody = {
        channel_types: channelSettings,
        category_channel_types: categoryChannelSettings
      };
      // logInfo('🔧 DEBUG: Channel preferences request body', requestBody);

      const response = await fetch(
        `${process.env.KNOCK_API_URL}/users/${userId}/preferences/default`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${process.env.KNOCK_SECRET_API_KEY}`,
          },
          body: JSON.stringify(requestBody),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        console.error('❌ Knock channel preferences error response:', {
          status: response.status,
          statusText: response.statusText,
          errorData
        });
        throw new Error(
          `Failed to set channel preferences: ${response.status} ${errorData.message || response.statusText
          }`
        );
      }

      return await response.json();
    } catch (error) {
      logError("Error setting channel preferences in Knock", error);
      throw error;
    }
  }

  /**
   * Sends a notification to a user.
   */
  async notify(template, userId, data = {}) {
    try {
      return await this.knock.workflows.trigger(template, {
        recipients: [userId],
        data,
      });
    } catch (error) {
      // Handle workflow not found errors gracefully
      if (error.message && error.message.includes('workflow you specified was not found')) {
        console.warn(`⚠️ Knock workflow '${template}' not found in this environment - skipping notification`);
        return { status: 'skipped_workflow_not_found' };
      }
      logError("Error sending notification via Knock", error);
      throw error;
    }
  }

  /**
   * Bulk sends a notification to multiple users.
   */
  async bulkNotify(template, userIds, data = {}) {
    try {
      return await this.knock.workflows.trigger(template, {
        recipients: userIds,
        data,
      });
    } catch (error) {
      // Handle workflow not found errors gracefully
      if (error.message && error.message.includes('workflow you specified was not found')) {
        console.warn(`⚠️ Knock workflow '${template}' not found in this environment - skipping bulk notification`);
        return { status: 'skipped_workflow_not_found' };
      }
      logError("Error bulk sending notifications via Knock", error);
      throw error;
    }
  }

  /**
   * Deletes a user from Knock.
   */
  async deleteUser(userId) {
    try {
      const response = await fetch(`${process.env.KNOCK_API_URL}/users/${userId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${process.env.KNOCK_SECRET_API_KEY}`,
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          `Failed to delete user: ${response.status} ${errorData.message || response.statusText
          }`
        );
      }

      return true;
    } catch (error) {
      logError("Failed to delete Knock user", error);
      throw error;
    }
  }
}
