-- ROLLBACK Migration: Revert Duplicate Payment Fix (USE ONLY IF NEEDED)
-- Date: 2025-07-01
-- Purpose: Rollback the duplicate payment fix if business requirements demand it
-- WARNING: This will restore the original behavior that creates payment records
--          during subscription edits. Only use if absolutely necessary.

-- ============================================================================
-- ROLLBACK STEP 1: Restore Original Database Triggers
-- ============================================================================

-- Restore handle_subscription_type_change function to original behavior
CREATE OR REPLACE FUNCTION public.handle_subscription_type_change()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    IF NEW.subscription_type_id != OLD.subscription_type_id THEN
        INSERT INTO subscription_history (
            subscription_id,
            payment_date,
            amount,
            status,
            type,
            previous_subscription_type_id,
            new_subscription_type_id,
            notes,
            created_by
        ) VALUES (
            NEW.id,
            NOW(),
            NEW.actual_price,
            'paid',  -- ⚠️ RESTORED: Back to 'paid' status
            'subscription_type_change',
            OLD.subscription_type_id,
            NEW.subscription_type_id,
            'Subscription type changed',
            auth.uid()
        );
    END IF;
    RETURN NEW;
END;
$function$;

-- Restore handle_promo_change function to original behavior
CREATE OR REPLACE FUNCTION public.handle_promo_change()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    -- Only create history entry if there's an actual change in promo status or details
    IF (NEW.is_promo_active IS DISTINCT FROM OLD.is_promo_active) OR
       (NEW.is_promo_active = true AND (
           NEW.promo_price IS DISTINCT FROM OLD.promo_price OR
           NEW.promo_cycles IS DISTINCT FROM OLD.promo_cycles
       ))
    THEN
        INSERT INTO subscription_history (
            subscription_id,
            payment_date,
            amount,
            status,
            type,
            is_promo_active,
            promo_price,
            promo_cycles,
            notes,
            created_by
        ) VALUES (
            NEW.id,
            NOW(),
            NEW.actual_price,
            'paid',  -- ⚠️ RESTORED: Back to 'paid' status
            'promo_change',
            NEW.is_promo_active,
            NEW.promo_price,
            NEW.promo_cycles,
            CASE
                WHEN NEW.is_promo_active THEN 'Promo activated: ' || NEW.promo_price || ' for ' || NEW.promo_cycles || ' cycles'
                ELSE 'Promo deactivated'
            END,
            auth.uid()
        );
    END IF;
    RETURN NEW;
END;
$function$;

-- Restore handle_discount_change function to original behavior
CREATE OR REPLACE FUNCTION public.handle_discount_change()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    IF (NEW.is_discount_active != OLD.is_discount_active) OR
       (NEW.discount_amount IS DISTINCT FROM OLD.discount_amount) OR
       (NEW.discount_type IS DISTINCT FROM OLD.discount_type) THEN
        INSERT INTO subscription_history (
            subscription_id,
            payment_date,
            amount,
            status,
            type,
            is_discount_active,
            discount_amount,
            discount_type,
            notes,
            created_by
        ) VALUES (
            NEW.id,
            NOW(),
            NEW.actual_price,
            'paid',  -- ⚠️ RESTORED: Back to 'paid' status
            'discount_change',
            NEW.is_discount_active,
            NEW.discount_amount,
            NEW.discount_type,
            CASE
                WHEN NEW.is_discount_active THEN 'Discount activated: ' || NEW.discount_amount || ' (' || NEW.discount_type || ')'
                ELSE 'Discount deactivated'
            END,
            auth.uid()
        );
    END IF;
    RETURN NEW;
END;
$function$;

-- ============================================================================
-- ROLLBACK STEP 2: Restore Original Payment Processing Trigger
-- ============================================================================

-- Restore update_subscription_on_payment to original behavior
CREATE OR REPLACE FUNCTION public.update_subscription_on_payment()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    v_subscription subscriptions;
    v_subscription_type subscription_types;
BEGIN
    -- Only proceed if payment is marked as paid (original behavior)
    IF NEW.status = 'paid' THEN
        -- Get subscription details
        SELECT * INTO v_subscription
        FROM subscriptions
        WHERE id = NEW.subscription_id;

        -- Get subscription type
        SELECT * INTO v_subscription_type
        FROM subscription_types
        WHERE id = v_subscription.subscription_type_id;

        -- Update subscription if not a lifetime subscription
        IF v_subscription_type.days > 0 THEN
            UPDATE subscriptions
            SET
                last_paid_date = NEW.payment_date,
                next_payment_date = NEW.payment_date + (v_subscription_type.days * INTERVAL '1 day')
            WHERE id = NEW.subscription_id;
        END IF;
    END IF;

    RETURN NEW;
END;
$function$;

-- ============================================================================
-- ROLLBACK STEP 3: Remove Duplicate Prevention (if desired)
-- ============================================================================

-- Remove the duplicate prevention trigger
DROP TRIGGER IF EXISTS prevent_duplicate_payments_trigger ON public.subscription_history;

-- Remove the duplicate prevention function
DROP FUNCTION IF EXISTS public.prevent_duplicate_payments();

-- ============================================================================
-- ROLLBACK STEP 4: Update Comments
-- ============================================================================

COMMENT ON FUNCTION public.handle_subscription_type_change() IS 
'ROLLBACK: Creates payment records (status=paid) when subscription type changes. This restores original behavior.';

COMMENT ON FUNCTION public.handle_promo_change() IS 
'ROLLBACK: Creates payment records (status=paid) when promo settings change. This restores original behavior.';

COMMENT ON FUNCTION public.handle_discount_change() IS 
'ROLLBACK: Creates payment records (status=paid) when discount settings change. This restores original behavior.';

COMMENT ON FUNCTION public.update_subscription_on_payment() IS 
'ROLLBACK: Updates subscription dates for all records with status=paid. This restores original behavior.';

-- ============================================================================
-- ROLLBACK WARNING
-- ============================================================================

DO $$
BEGIN
    RAISE NOTICE '⚠️  ROLLBACK COMPLETE: Original behavior restored';
    RAISE NOTICE '⚠️  WARNING: Subscription edits will now create payment records again';
    RAISE NOTICE '⚠️  WARNING: Duplicate payment issues may return';
    RAISE NOTICE '⚠️  Consider applying the fix again: 20250701000001_fix_duplicate_payments.sql';
END $$;
