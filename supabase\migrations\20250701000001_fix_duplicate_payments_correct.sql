-- Migration: Fix Duplicate Payment Creation During Subscription Edits
-- Date: 2025-07-01
-- Purpose: Fix triggers that incorrectly mark change tracking records as 'paid'
--          Change tracking records should use status 'none', not 'paid'

-- ============================================================================
-- STEP 1: Fix Database Triggers - Change status from 'paid' to 'none'
-- ============================================================================

-- Fix handle_subscription_type_change function
CREATE OR REPLACE FUNCTION public.handle_subscription_type_change()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    IF NEW.subscription_type_id != OLD.subscription_type_id THEN
        INSERT INTO subscription_history (
            subscription_id,
            payment_date,
            amount,
            status,
            type,
            previous_subscription_type_id,
            new_subscription_type_id,
            notes,
            created_by
        ) VALUES (
            NEW.id,
            NOW(),
            NEW.actual_price,
            'none',  -- ✅ FIXED: Changed from 'paid' to 'none' (this is change tracking, not a payment)
            'subscription_type_change',
            OLD.subscription_type_id,
            NEW.subscription_type_id,
            'Subscription type changed',
            auth.uid()
        );
    END IF;
    RETURN NEW;
END;
$function$;

-- Fix handle_promo_change function
CREATE OR REPLACE FUNCTION public.handle_promo_change()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    -- Only create history entry if there's an actual change in promo status or details
    IF (NEW.is_promo_active IS DISTINCT FROM OLD.is_promo_active) OR
       (NEW.is_promo_active = true AND (
           NEW.promo_price IS DISTINCT FROM OLD.promo_price OR
           NEW.promo_cycles IS DISTINCT FROM OLD.promo_cycles
       ))
    THEN
        INSERT INTO subscription_history (
            subscription_id,
            payment_date,
            amount,
            status,
            type,
            is_promo_active,
            promo_price,
            promo_cycles,
            notes,
            created_by
        ) VALUES (
            NEW.id,
            NOW(),
            NEW.actual_price,
            'none',  -- ✅ FIXED: Changed from 'paid' to 'none' (this is change tracking, not a payment)
            'promo_change',
            NEW.is_promo_active,
            NEW.promo_price,
            NEW.promo_cycles,
            CASE
                WHEN NEW.is_promo_active THEN 'Promo activated: ' || NEW.promo_price || ' for ' || NEW.promo_cycles || ' cycles'
                ELSE 'Promo deactivated'
            END,
            auth.uid()
        );
    END IF;
    RETURN NEW;
END;
$function$;

-- Fix handle_discount_change function
CREATE OR REPLACE FUNCTION public.handle_discount_change()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
    IF (NEW.is_discount_active != OLD.is_discount_active) OR
       (NEW.discount_amount IS DISTINCT FROM OLD.discount_amount) OR
       (NEW.discount_type IS DISTINCT FROM OLD.discount_type) THEN
        INSERT INTO subscription_history (
            subscription_id,
            payment_date,
            amount,
            status,
            type,
            is_discount_active,
            discount_amount,
            discount_type,
            notes,
            created_by
        ) VALUES (
            NEW.id,
            NOW(),
            NEW.actual_price,
            'none',  -- ✅ FIXED: Changed from 'paid' to 'none' (this is change tracking, not a payment)
            'discount_change',
            NEW.is_discount_active,
            NEW.discount_amount,
            NEW.discount_type,
            CASE
                WHEN NEW.is_discount_active THEN 'Discount activated: ' || NEW.discount_amount || ' (' || NEW.discount_type || ')'
                ELSE 'Discount deactivated'
            END,
            auth.uid()
        );
    END IF;
    RETURN NEW;
END;
$function$;

-- ============================================================================
-- STEP 2: Update payment trigger to only process actual payments
-- ============================================================================

-- Update the payment processing trigger to only process actual payments
CREATE OR REPLACE FUNCTION public.update_subscription_on_payment()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    v_subscription subscriptions;
    v_subscription_type subscription_types;
BEGIN
    -- Only proceed if this is an actual payment (not a change tracking record)
    IF NEW.status = 'paid' AND NEW.type IN ('payment', 'credit') THEN
        -- Get subscription details
        SELECT * INTO v_subscription
        FROM subscriptions
        WHERE id = NEW.subscription_id;

        -- Get subscription type
        SELECT * INTO v_subscription_type
        FROM subscription_types
        WHERE id = v_subscription.subscription_type_id;

        -- Update subscription if not a lifetime subscription
        IF v_subscription_type.days > 0 THEN
            UPDATE subscriptions
            SET
                last_paid_date = NEW.payment_date,
                next_payment_date = NEW.payment_date + (v_subscription_type.days * INTERVAL '1 day')
            WHERE id = NEW.subscription_id;
        END IF;
    END IF;

    RETURN NEW;
END;
$function$;

-- ============================================================================
-- STEP 3: Add deduplication function for payment records
-- ============================================================================

-- Function to check for duplicate payment records
CREATE OR REPLACE FUNCTION public.prevent_duplicate_payments()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
    duplicate_count INTEGER;
BEGIN
    -- Only check for duplicates on actual payments
    IF NEW.status = 'paid' AND NEW.type IN ('payment', 'credit') THEN
        -- Check for existing payment records with same subscription, date, amount, and type
        SELECT COUNT(*)
        INTO duplicate_count
        FROM subscription_history
        WHERE subscription_id = NEW.subscription_id
          AND payment_date = NEW.payment_date
          AND amount = NEW.amount
          AND status = 'paid'
          AND type = NEW.type
          AND id != COALESCE(NEW.id, 0); -- Exclude current record if updating

        -- Prevent duplicate if found
        IF duplicate_count > 0 THEN
            RAISE EXCEPTION 'Duplicate payment record detected for subscription % on date % with amount %', 
                NEW.subscription_id, NEW.payment_date, NEW.amount;
        END IF;
    END IF;

    RETURN NEW;
END;
$function$;

-- Create trigger for duplicate prevention
DROP TRIGGER IF EXISTS prevent_duplicate_payments_trigger ON public.subscription_history;
CREATE TRIGGER prevent_duplicate_payments_trigger
    BEFORE INSERT OR UPDATE ON public.subscription_history
    FOR EACH ROW
    EXECUTE FUNCTION prevent_duplicate_payments();

-- ============================================================================
-- STEP 4: Fix existing incorrect records
-- ============================================================================

-- Update existing price_change records that are incorrectly marked as 'paid'
UPDATE subscription_history 
SET status = 'none'
WHERE type = 'price_change' 
  AND status = 'paid';

-- Update existing change tracking records that are incorrectly marked as 'paid'
UPDATE subscription_history 
SET status = 'none'
WHERE type IN ('subscription_type_change', 'promo_change', 'discount_change') 
  AND status = 'paid';

-- ============================================================================
-- STEP 5: Add comments for clarity
-- ============================================================================

COMMENT ON FUNCTION public.handle_subscription_type_change() IS 
'Creates change tracking records (status=none) when subscription type changes. These are NOT payments.';

COMMENT ON FUNCTION public.handle_promo_change() IS 
'Creates change tracking records (status=none) when promo settings change. These are NOT payments.';

COMMENT ON FUNCTION public.handle_discount_change() IS 
'Creates change tracking records (status=none) when discount settings change. These are NOT payments.';

COMMENT ON FUNCTION public.update_subscription_on_payment() IS 
'Updates subscription dates only for actual payments (status=paid AND type IN (payment, credit)). Ignores change tracking records.';

COMMENT ON FUNCTION public.prevent_duplicate_payments() IS 
'Prevents duplicate payment records with same subscription, date, amount, and type. Only applies to actual payments, not change tracking records.';
