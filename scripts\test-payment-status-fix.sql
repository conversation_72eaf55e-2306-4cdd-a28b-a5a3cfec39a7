-- Test script to verify payment status fix
-- This script tests that change tracking records use status 'none', not 'paid'

-- ============================================================================
-- Check current status distribution
-- ============================================================================

SELECT 
    'Current Status Distribution' as test_name,
    type,
    status,
    COUNT(*) as count
FROM subscription_history 
GROUP BY type, status
ORDER BY type, status;

-- ============================================================================
-- Check for problematic records (change types with 'paid' status)
-- ============================================================================

SELECT 
    'Problematic Records Check' as test_name,
    COUNT(*) as problematic_count,
    CASE 
        WHEN COUNT(*) = 0 THEN '✅ PASS - No change tracking records marked as paid'
        ELSE '❌ FAIL - Found change tracking records marked as paid'
    END as result
FROM subscription_history 
WHERE type IN ('price_change', 'subscription_type_change', 'promo_change', 'discount_change')
  AND status = 'paid';

-- ============================================================================
-- Show details of any problematic records
-- ============================================================================

SELECT 
    'Problematic Record Details' as test_name,
    id,
    subscription_id,
    type,
    status,
    amount,
    payment_date,
    notes
FROM subscription_history 
WHERE type IN ('price_change', 'subscription_type_change', 'promo_change', 'discount_change')
  AND status = 'paid'
ORDER BY created_at DESC
LIMIT 10;

-- ============================================================================
-- Verify actual payments are still marked correctly
-- ============================================================================

SELECT 
    'Actual Payments Check' as test_name,
    COUNT(*) as payment_count,
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ PASS - Found actual payments marked as paid'
        ELSE '⚠️  INFO - No actual payments found (this may be normal)'
    END as result
FROM subscription_history 
WHERE type IN ('payment', 'credit')
  AND status = 'paid';

-- ============================================================================
-- Summary by type and status
-- ============================================================================

SELECT 
    '=== SUMMARY: Expected Status by Type ===' as summary;

SELECT 
    type,
    status,
    COUNT(*) as count,
    CASE 
        WHEN type IN ('payment', 'credit') AND status = 'paid' THEN '✅ Correct'
        WHEN type IN ('payment', 'credit') AND status = 'missed' THEN '✅ Correct'
        WHEN type IN ('price_change', 'subscription_type_change', 'promo_change', 'discount_change') AND status = 'none' THEN '✅ Correct'
        WHEN type IN ('price_change', 'subscription_type_change', 'promo_change', 'discount_change') AND status = 'paid' THEN '❌ INCORRECT'
        ELSE '⚠️  Review'
    END as assessment
FROM subscription_history 
GROUP BY type, status
ORDER BY 
    CASE type 
        WHEN 'payment' THEN 1
        WHEN 'credit' THEN 2
        WHEN 'price_change' THEN 3
        WHEN 'subscription_type_change' THEN 4
        WHEN 'promo_change' THEN 5
        WHEN 'discount_change' THEN 6
        ELSE 7
    END,
    status;
