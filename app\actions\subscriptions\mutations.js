/**
 * Subscription Mutation Actions
 *
 * Purpose: Server-side actions for creating, updating, and deleting subscriptions.
 * Handles all write operations for subscription data with validation and
 * transformation logic.
 *
 * Key features:
 * - Creates new subscriptions with full validation
 * - Updates existing subscription data
 * - Handles subscription deletion
 * - Manages subscription sharing and pausing
 * - Validates and transforms data for database storage
 * - Sends admin notifications for important events
 * - Revalidates Next.js cache after mutations
 *
 * SECURITY: All functions authenticate users and verify ownership before operations
 */

"use server";
import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import {
  validateSubscriptionData,
  transformForDatabase,
  transformForUI,
} from "@/utils/subscription-validator";
import { notifyAdmins } from "@/app/actions/admin/notifications";
import { withTimeout, withTimeoutAndLogging, tagsHaveChanged, TIMEOUT_CONFIG } from "@/utils/database-helpers";

export async function createSubscription(data) {
  const supabase = await createClient();

  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  const warnings = {};

  // Clean up the subscription data
  const subscriptionData = {
    ...data,
    subscription: {
      ...data.subscription,
      user_id: user.id, // Set the correct user_id from authenticated user
    },
  };

  // Validate and get transformed data
  const {
    isValid,
    errors,
    data: validatedData,
  } = validateSubscriptionData(subscriptionData);
  if (!isValid) {
    console.error("Validation errors:", errors);
    throw new Error(Object.values(errors)[0] || "Invalid subscription data");
  }

  // Handle Brandfetch company creation if needed
  if (validatedData.subscription.company_id?.isBrandfetch) {
    const companyData = validatedData.subscription.company_id;
    console.log("Brandfetch company data:", companyData);

    // Validate that we have at least a website to work with
    if (!companyData.website?.trim()) {
      throw new Error("Company website is required for Brandfetch companies");
    }

    // Check if company already exists by checking:
    // 1. Exact website match
    // 2. Website as substring (for subpages)
    // 3. Similar company name (only if label exists)
    let lookupQuery = `website.eq."${companyData.website}",` +
                     `website.ilike."%${companyData.website.split('/')[0]}%"`;

    // Only add name lookup if we have a valid label
    if (companyData.label?.trim()) {
      lookupQuery += `,name.ilike."%${companyData.label}%"`;
    }

    const { data: existingCompanies, error: lookupError } = await supabase
      .from("companies")
      .select("id, name, website")
      .or(lookupQuery);

    console.log("Existing company lookup:", { existingCompanies, lookupError });

    let existingCompany = null;

    if (existingCompanies?.length > 0) {
      // First, try to find an exact website match
      existingCompany = existingCompanies.find(c => c.website === companyData.website);

      if (!existingCompany) {
        // Then, try to find a base domain match (e.g., github.com matches github.com/features/copilot)
        const baseWebsite = companyData.website.split('/')[0];
        existingCompany = existingCompanies.find(c =>
          c.website?.split('/')[0] === baseWebsite
        );

        // If we found a base domain match and URLs are different, this is likely a product/feature
        if (existingCompany) {
          const currentDesc = validatedData.subscription.description?.trim() || '';
          const productInfo = `\n\nThis is a ${existingCompany.name} product/feature.` +
            `\nProduct URL: ${companyData.website}` +
            `\nMain Company: ${existingCompany.name} (${existingCompany.website})`;
          validatedData.subscription.description = currentDesc + productInfo;
        }
      }

      if (!existingCompany) {
        // Finally, use the first company with a matching name
        existingCompany = existingCompanies[0];

        // Add a note about using name-matched company
        const currentDesc = validatedData.subscription.description?.trim() || '';
        const matchInfo = `\n\nNote: Using existing company record for ${existingCompany.name}.` +
          `\nCompany Website: ${existingCompany.website}` +
          `\nProduct Website: ${companyData.website}`;
        validatedData.subscription.description = currentDesc + matchInfo;
      }
    }

    if (existingCompany) {
      // Use existing company
      console.log("Using existing company:", existingCompany.id);
      validatedData.subscription.company_id = existingCompany.id;

      // If the URLs are different, append product info to description
      if (companyData.website !== existingCompany.website) {
        const currentDesc = validatedData.subscription.description?.trim() || '';
        const productInfo = `\n\nProduct URL: ${companyData.website}`;
        validatedData.subscription.description = currentDesc + productInfo;
      }
    } else {
      // Create new company
      // Fallback logic for missing company name
      let companyName = companyData.label?.trim();

      if (!companyName && companyData.website) {
        // Extract company name from website domain
        try {
          let domain = companyData.website.trim();
          // Remove protocol if present
          domain = domain.replace(/^https?:\/\//, '');
          // Remove www. if present
          domain = domain.replace(/^www\./, '');
          // Remove path and query parameters
          domain = domain.split('/')[0].split('?')[0];
          // Capitalize first letter and remove extension
          companyName = domain.split('.')[0];
          companyName = companyName.charAt(0).toUpperCase() + companyName.slice(1);
        } catch (error) {
          console.error("Error extracting company name from website:", error);
          companyName = "Unknown Company";
        }
      }

      if (!companyName) {
        companyName = "Unknown Company";
      }

      const { data: company, error: companyError } = await supabase
        .from("companies")
        .insert({
          name: companyName,
          website: companyData.website,
          icon: companyData.icon,
          is_public: validatedData.newCompany?.is_public || false,
          is_approved: false,
          created_by: user.id,
        })
        .select("id")
        .single();

      if (companyError)
        throw new Error("Failed to create company: " + companyError.message);
      console.log("Created new company:", company.id);
      validatedData.subscription.company_id = company.id;

      // Notify admins if company is public
      if (validatedData.newCompany?.is_public) {
        await notifyAdmins({
          resourceType: "company",
          resourceId: company.id,
          userId: user.id,
          subject: "New Company Approval Required",
          content: `A new company has been submitted for approval:
            Company: ${companyName}
            Website: ${companyData.website}`,
          metadata: {
            company_name: companyName,
            company_website: companyData.website,
          },
        });
      }
    }
  }

  // Transform data for database
  const dbData = await transformForDatabase(
    validatedData,
    user.id,
    data.usePersonalEncryption
  );

  // Fix bucket_id format before inserting
  if (dbData.user_bucket_id && typeof dbData.user_bucket_id === 'object') {
    if (dbData.user_bucket_id.value) {
      dbData.user_bucket_id = dbData.user_bucket_id.value;
    } else {
      // If no value, it's a new bucket name, set to null and handle after insert
      dbData.user_bucket_id = null;
    }
  }

  console.log("Final subscription data:", {
    originalCompanyId: validatedData.subscription.company_id,
    transformedCompanyId: dbData.company_id,
    dbData,
  });

  // Create subscription
  const { data: sub, error: subscriptionError } = await supabase
    .from("subscriptions")
    .insert([dbData])
    .select("id")
    .single();

  if (subscriptionError) throw subscriptionError;

  // Store the ID for later use
  const subscriptionId = sub.id;

  // Handle bucket first
  const bucketName = validatedData.subscription.user_bucket_id;
  if (typeof bucketName === "string") {
    try {
      // Check if bucket exists
      const { data: existingBucket } = await supabase
        .from("user_buckets")
        .select("id")
        .eq("name", bucketName)
        .eq("user_id", user.id)
        .single();

      if (existingBucket) {
        // Update subscription with existing bucket
        const { error: bucketError } = await supabase
          .from("subscriptions")
          .update({ user_bucket_id: existingBucket.id })
          .eq("id", subscriptionId)
          .eq("user_id", user.id); // Ensure ownership

        if (bucketError) throw bucketError;
      } else {
        // Create new bucket
        const { data: newBucket, error: bucketError } = await supabase
          .from("user_buckets")
          .insert({
            name: bucketName,
            user_id: user.id,
            created_by: user.id,
          })
          .select("id")
          .single();

        if (bucketError)
          throw new Error("Failed to create bucket: " + bucketError.message);

        // Update subscription with new bucket
        const { error: updateError } = await supabase
          .from("subscriptions")
          .update({ user_bucket_id: newBucket.id })
          .eq("id", subscriptionId)
          .eq("user_id", user.id); // Ensure ownership

        if (updateError) throw updateError;
      }
    } catch (error) {
      warnings.bucket = {
        error: `Failed to process bucket: ${error.message}`,
        action: "Configure Bucket",
        pendingData: {
          label: bucketName,
        },
      };
    }
  }

  // Non-critical: Try to add tags
  if (validatedData.subscription.tags?.length > 0) {
    try {
      const tags = validatedData.subscription.tags || [];
      const tagIds = [];

      for (const tag of tags) {
        if (typeof tag === "string") {
          // This is a new tag, create it
          const { data: existingTag } = await supabase
            .from("tags")
            .select("id")
            .eq("name", tag)
            .single();

          if (existingTag) {
            tagIds.push(existingTag.id);
          } else {
            const { data: newTag, error: tagError } = await supabase
              .from("tags")
              .insert({ name: tag })
              .select("id")
              .single();

            if (tagError) throw tagError;
            tagIds.push(newTag.id);
          }
        } else {
          // This is an existing tag
          tagIds.push(tag.value);
        }
      }

      if (tagIds.length > 0) {
        const tagAssociations = tagIds.map((tagId) => ({
          subscription_id: subscriptionId,
          tag_id: tagId,
        }));

        const { error: associationError } = await supabase
          .from("subscription_tags")
          .insert(tagAssociations);

        if (associationError) throw associationError;
      }
    } catch (error) {
      warnings.tags = {
        error: `Failed to process tags: ${error.message}`,
        action: "Configure Tags",
        pendingData: validatedData.subscription.tags,
      };
    }
  }

  // Update alert profile if needed
  if (
    validatedData.subscription.has_alerts &&
    validatedData.subscription.alert_profile_id
  ) {
    try {
      const { error: alertError } = await supabase
        .from("subscriptions")
        .update({
          alert_profile_id: validatedData.subscription.alert_profile_id,
        })
        .eq("id", subscriptionId)
        .eq("user_id", user.id); // Ensure ownership

      if (alertError) throw alertError;
    } catch (error) {
      warnings.alerts = {
        error: `Failed to set alert profile: ${error.message}`,
        action: "Configure Alerts",
        pendingData: {
          name: validatedData.subscription.alert_profile_id,
        },
      };
    }
  }

  // Update custom fields if any
  if (Object.keys(validatedData.subscription.custom_fields || {}).length > 0) {
    try {
      const { error: customFieldsError } = await supabase
        .from("subscriptions")
        .update({ custom_fields: validatedData.subscription.custom_fields })
        .eq("id", subscriptionId)
        .eq("user_id", user.id); // Ensure ownership

      if (customFieldsError) throw customFieldsError;
    } catch (error) {
      warnings.customFields = {
        error: `Failed to save custom fields: ${error.message}`,
        action: "Configure Custom Fields",
        pendingData: validatedData.subscription.custom_fields,
      };
    }
  }

  revalidatePath("/dashboard");
  return {
    success: true,
    subscription: transformForUI(dbData),
    warnings: Object.keys(warnings).length > 0 ? warnings : undefined,
  };
}

export async function updateSubscription(shortId, data) {
  const supabase = await createClient();
  const startTime = performance.now();

  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  // Helper function to add timeout to database operations
  const withTimeout = (promise, operation = "database operation", timeoutMs = 15000) => {
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => {
        const error = new Error(`Timeout: ${operation} exceeded ${timeoutMs}ms`);
        error.code = 'OPERATION_TIMEOUT';
        reject(error);
      }, timeoutMs);
    });
    return Promise.race([promise, timeoutPromise]);
  };

  try {
    console.log(`🔄 Starting subscription update for ${shortId}`);

    // Get current subscription data for comparison and verify ownership
    // Fetch all fields we need for the update operation
    const fetchStartTime = performance.now();
    const fetchPromise = supabase
      .from("subscriptions")
      .select(`
        id,
        short_id,
        user_id,
        custom_fields,
        actual_price,
        regular_price,
        user_profiles:profiles!inner(use_own_encryption_key)
      `)
      .eq("short_id", shortId)
      .eq("user_id", user.id) // Verify ownership
      .is("deleted_at", null)
      .single();

    const { data: currentSubscription, error: fetchError } = await withTimeout(
      fetchPromise,
      "subscription fetch",
      10000
    );

    console.log(`⏱️ Subscription fetch took ${(performance.now() - fetchStartTime).toFixed(2)}ms`);

    if (fetchError || !currentSubscription) {
      throw new Error("Subscription not found or access denied");
    }

    // Check if user uses personal encryption
    const usePersonalEncryption = currentSubscription.user_profiles.use_own_encryption_key;

    // Destructure tags and custom_fields, keep remaining subscription data
    const { tags, custom_fields, ...subscriptionData } = data.subscription;

    // Track price changes if price is being updated
    if (subscriptionData.actual_price !== currentSubscription.actual_price) {
      const { error: historyError } = await supabase
        .from("subscription_history")
        .insert({
          subscription_id: currentSubscription.id,
          payment_date: new Date(),
          amount: subscriptionData.actual_price ?? 0,
          status: "history", // ✅ FIXED: Changed from 'paid' to 'history' to prevent triggering payment processing
          type: "price_change",
          previous_amount: currentSubscription.actual_price ?? 0,
          new_amount: subscriptionData.actual_price ?? 0,
          notes: `Price changed from ${currentSubscription.actual_price} to ${subscriptionData.actual_price}`,
        });

      if (historyError) throw historyError;
    }

    // Update main subscription data
    const updateStartTime = performance.now();
    const updatePromise = supabase
      .from("subscriptions")
      .update({
        ...subscriptionData,
        alert_profile_id: subscriptionData.alert_profile_id || null, // Ensure proper alert handling
      })
      .eq("short_id", shortId)
      .eq("user_id", user.id); // Ensure ownership

    const { error: updateError } = await withTimeout(
      updatePromise,
      "subscription update",
      10000
    );

    console.log(`⏱️ Subscription update took ${(performance.now() - updateStartTime).toFixed(2)}ms`);

    if (updateError) throw updateError;

    // Handle tags - always update tags when they are provided (even if empty array)
    if (tags !== undefined) {
      const tagStartTime = performance.now();

      // Get current tag associations to compare
      const currentTagsPromise = supabase
        .from("subscription_tags")
        .select("tag_id")
        .eq("subscription_id", currentSubscription.id);

      const { data: currentTags } = await withTimeout(
        currentTagsPromise,
        "current tags fetch",
        5000
      );

      const currentTagIds = currentTags?.map(t => t.tag_id) || [];
      const newTagIds = tags.map(tag => tag.value || tag.id);

      // Only update if tags have actually changed
      const tagsChanged = tagsHaveChanged(currentTagIds, newTagIds);

      if (tagsChanged) {
        // Use transaction-like approach: delete then insert
        const deletePromise = supabase
          .from("subscription_tags")
          .delete()
          .eq("subscription_id", currentSubscription.id);

        await withTimeout(deletePromise, "tag deletion", 5000);

        // Insert new tag associations if there are any
        if (tags.length > 0) {
          const insertPromise = supabase
            .from("subscription_tags")
            .insert(
              tags.map((tag) => ({
                subscription_id: currentSubscription.id,
                tag_id: tag.value || tag.id, // Handle both new and existing tags
              }))
            );

          const { error: tagError } = await withTimeout(
            insertPromise,
            "tag insertion",
            5000
          );

          if (tagError) throw tagError;
        }

        console.log(`⏱️ Tag update took ${(performance.now() - tagStartTime).toFixed(2)}ms`);
      } else {
        console.log(`⏭️ Tags unchanged, skipping update`);
      }
    }

    // Handle custom fields if they exist
    if (custom_fields) {
      // Get current custom fields to preserve encrypted values
      const currentCustomFields = currentSubscription.custom_fields || { data: {}, metadata: { encrypted_fields: [] } };
      const updatedCustomFields = {
        data: {},
        metadata: { encrypted_fields: [] }
      };

      // Process each field
      Object.entries(custom_fields.data || {}).forEach(([key, field]) => {
        // If the field is already encrypted and hasn't changed, preserve it
        if (currentCustomFields.metadata.encrypted_fields.includes(key) &&
          currentCustomFields.data[key] === field.value) {
          updatedCustomFields.data[key] = field.value;
          updatedCustomFields.metadata.encrypted_fields.push(key);
        } else {
          // For new or modified fields, just store the value
          updatedCustomFields.data[key] = field.value;
          // Only mark for encryption if using system key or if personal key is provided
          if (field.encrypt && (!usePersonalEncryption || data.personalKey)) {
            updatedCustomFields.metadata.encrypted_fields.push(key);
          }
        }
      });

      // If using system encryption, encrypt on server side
      if (!usePersonalEncryption && updatedCustomFields.metadata.encrypted_fields.length > 0) {
        const { processEncryptedFields } = await import("@/utils/encryption");
        const processedFields = await processEncryptedFields(
          { data: updatedCustomFields.data },
          null, // No personal key needed
          true // Server-side encryption
        );
        updatedCustomFields.data = processedFields.data;
      }

      const fieldsStartTime = performance.now();
      const fieldsPromise = supabase
        .from("subscriptions")
        .update({ custom_fields: updatedCustomFields })
        .eq("short_id", shortId)
        .eq("user_id", user.id); // Ensure ownership

      const { error: fieldsError } = await withTimeout(
        fieldsPromise,
        "custom fields update",
        5000
      );

      console.log(`⏱️ Custom fields update took ${(performance.now() - fieldsStartTime).toFixed(2)}ms`);

      if (fieldsError) throw fieldsError;
    }

    const totalTime = (performance.now() - startTime).toFixed(2);
    console.log(`✅ Subscription update completed in ${totalTime}ms`);

    revalidatePath("/dashboard");
    return { success: true };
  } catch (error) {
    const totalTime = (performance.now() - startTime).toFixed(2);

    // Enhanced error logging with context
    console.error(`❌ Subscription update failed after ${totalTime}ms:`, {
      shortId,
      error: error.message,
      code: error.code,
      stack: error.stack
    });

    // Add Sentry context for better debugging
    if (typeof window === 'undefined') { // Server-side only
      const Sentry = await import("@sentry/nextjs");
      Sentry.captureException(error, {
        tags: {
          operation: 'updateSubscription',
          shortId: shortId
        },
        extra: {
          executionTime: totalTime,
          errorCode: error.code,
          isTimeout: error.code === 'OPERATION_TIMEOUT'
        },
        contexts: {
          subscription: {
            shortId: shortId,
            hasCustomFields: !!data.custom_fields,
            hasTags: !!data.tags,
            operationDuration: totalTime
          }
        }
      });
    }

    throw error;
  }
}

export async function deleteSubscription(subscriptionId) {
  const supabase = await createClient();

  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    // If subscriptionId is a string starting with 'sub-', it's a shortId
    const isShortId = typeof subscriptionId === 'string' && subscriptionId.startsWith('sub-');

    // First verify ownership and that it's not already deleted
    const { data: subscription, error: verifyError } = await supabase
      .from("subscriptions")
      .select("id, deleted_at")
      .eq(isShortId ? "short_id" : "id", subscriptionId)
      .eq("user_id", user.id)
      .single();

    if (verifyError || !subscription) {
      throw new Error("Subscription not found or access denied");
    }

    if (subscription.deleted_at) {
      throw new Error("Subscription is already deleted");
    }

    // Soft delete by setting deleted_at timestamp
    const { error } = await supabase
      .from("subscriptions")
      .update({ deleted_at: new Date().toISOString() })
      .eq(isShortId ? "short_id" : "id", subscriptionId)
      .eq("user_id", user.id); // Double ensure ownership

    if (error) throw error;

    revalidatePath("/dashboard");
    return { success: true };
  } catch (error) {
    console.error("Error deleting subscription:", error);
    throw error;
  }
}

export async function restoreSubscription(subscriptionId) {
  const supabase = await createClient();

  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  try {
    // If subscriptionId is a string starting with 'sub-', it's a shortId
    const isShortId = typeof subscriptionId === 'string' && subscriptionId.startsWith('sub-');

    // First verify ownership and that it's actually deleted
    const { data: subscription, error: verifyError } = await supabase
      .from("subscriptions")
      .select("id, deleted_at")
      .eq(isShortId ? "short_id" : "id", subscriptionId)
      .eq("user_id", user.id)
      .not("deleted_at", "is", null) // Only find soft-deleted records
      .single();

    if (verifyError || !subscription) {
      throw new Error("Deleted subscription not found or access denied");
    }

    // Restore by setting deleted_at to null
    const { error } = await supabase
      .from("subscriptions")
      .update({ deleted_at: null })
      .eq(isShortId ? "short_id" : "id", subscriptionId)
      .eq("user_id", user.id); // Double ensure ownership

    if (error) throw error;

    revalidatePath("/dashboard");
    return { success: true };
  } catch (error) {
    console.error("Error restoring subscription:", error);
    throw error;
  }
}

// export async function getSubscriptions() {
//   const supabase = await createClient();

//   // Get authenticated user
//   const { data: { user }, error: authError } = await supabase.auth.getUser();
//   if (authError || !user) {
//     throw new Error("Authentication required");
//   }

//   try {
//     const { data, error } = await supabase
//       .from("subscriptions")
//       .select(
//         `
//         *,
//         companies (id, name, icon, website, cancel_url),
//         subscription_types (id, name),
//         payment_types (id, name),
//         currencies (id, code, symbol),
//         bucket:user_buckets (id, name)
//       `
//       )
//       .eq("user_id", user.id) // Only get authenticated user's subscriptions
//       .order("next_payment_date", { ascending: false });

//     if (error) throw error;
//     return data;
//   } catch (error) {
//     console.error("Error in getSubscriptions:", error);
//     throw error;
//   }
// }

/**
 * Records a payment for a subscription
 * @param {Object} subscription - The subscription object
 * @returns {Promise<void>}
 */
export async function recordPayment(subscription) {
  const supabase = await createClient();

  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  // Verify ownership of subscription
  const { data: ownedSub, error: verifyError } = await supabase
    .from("subscriptions")
    .select("id")
    .eq("id", subscription.id)
    .eq("user_id", user.id)
    .is("deleted_at", null)
    .single();

  if (verifyError || !ownedSub) {
    throw new Error("Subscription not found or access denied");
  }

  const missedPayments = await getMissedPayments(subscription.id);

  // If no missed payments yet, create a new one as paid
  if (missedPayments.length === 0) {
    const { error: insertError } = await supabase
      .from("subscription_history")
      .insert({
        subscription_id: subscription.id,
        payment_date: subscription.next_payment_date,
        payment_type_id: subscription.payment_type_id,
        amount: subscription.actual_price,
        status: "paid",
        type: "payment"
      });

    if (insertError) throw insertError;

    // Update subscription's last_paid_date and payment_date
    const { error: updateError } = await supabase
      .from("subscriptions")
      .update({
        last_paid_date: subscription.next_payment_date,
        payment_date: subscription.next_payment_date, // This will trigger the next_payment_date update
      })
      .eq("id", subscription.id)
      .eq("user_id", user.id); // Ensure ownership

    if (updateError) throw updateError;
  }
  // For a single missed payment, mark it as paid
  else if (missedPayments.length === 1) {
    const payment = missedPayments[0];
    const { error: updateError } = await supabase
      .from("subscription_history")
      .update({ status: "paid" })
      .eq("id", payment.id);

    if (updateError) throw updateError;

    // Update subscription's last_paid_date and payment_date
    const { error: subUpdateError } = await supabase
      .from("subscriptions")
      .update({
        last_paid_date: payment.payment_date,
        payment_date: payment.payment_date, // This will trigger the next_payment_date update
      })
      .eq("id", subscription.id)
      .eq("user_id", user.id); // Ensure ownership

    if (subUpdateError) throw subUpdateError;
  }
  // For multiple missed payments, show modal
  else {
    return missedPayments;
  }
}

/**
 * Retrieves missed payments for a subscription
 * @param {string} subscriptionId - The ID of the subscription
 * @returns {Promise<Array>} Array of missed payments
 */
export async function getMissedPayments(subscriptionId) {
  const supabase = await createClient();

  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  // Verify ownership through subscription table
  const { data: subscription, error: verifyError } = await supabase
    .from("subscriptions")
    .select("id")
    .eq("id", subscriptionId)
    .eq("user_id", user.id)
    .is("deleted_at", null)
    .single();

  if (verifyError || !subscription) {
    throw new Error("Subscription not found or access denied");
  }

  const { data: payments, error } = await supabase
    .from("subscription_history")
    .select("*")
    .eq("subscription_id", subscriptionId)
    .eq("status", "missed")
    .eq("type", "payment")
    .order("payment_date", { ascending: true });

  if (error) throw error;
  return payments || [];
}

export async function archiveSubscription(subscriptionId) {
  const supabase = await createClient();

  // Get authenticated user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    throw new Error("Authentication required");
  }

  const { error } = await supabase
    .from("subscriptions")
    .update({ is_active: false })
    .eq("id", subscriptionId)
    .eq("user_id", user.id); // Ensure ownership

  if (error) throw error;

  revalidatePath("/dashboard");
  return { success: true };
}
